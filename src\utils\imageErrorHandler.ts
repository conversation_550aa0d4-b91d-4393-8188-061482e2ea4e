/**
 * Enhanced image error handling utilities for CORS-compliant image loading
 */

import { convertS3UrlToR2, isS3Url } from "./s3-to-r2-migration";

interface ImageErrorHandlerOptions {
  retryCount?: number;
  fallbackUrl?: string;
  onRetry?: (attempt: number, url: string) => void;
  onFallback?: (originalUrl: string, fallbackUrl?: string) => void;
  onFinalError?: (originalUrl: string) => void;
}

interface ImageLoadResult {
  success: boolean;
  finalUrl?: string;
  error?: string;
  attempts: number;
}

/**
 * Enhanced image loader with retry logic and fallback mechanisms
 */
export class ImageErrorHandler {
  private static readonly DEFAULT_RETRY_COUNT = 2;
  private static readonly RETRY_DELAY = 1000; // 1 second

  /**
   * Attempts to load an image with retry logic and fallbacks
   */
  static async loadImageWithRetry(
    originalUrl: string,
    options: ImageErrorHandlerOptions = {}
  ): Promise<ImageLoadResult> {
    const {
      retryCount = this.DEFAULT_RETRY_COUNT,
      fallbackUrl,
      onRetry,
      onFallback,
      onFinalError,
    } = options;

    let attempts = 0;
    let lastError = "";

    // Try the original URL first
    let urlToTry = originalUrl;

    // Convert S3 URLs to R2 URLs if needed
    if (isS3Url(originalUrl)) {
      urlToTry = convertS3UrlToR2(originalUrl);
    }

    for (let i = 0; i <= retryCount; i++) {
      attempts++;
      
      try {
        const success = await this.testImageLoad(urlToTry);
        if (success) {
          return {
            success: true,
            finalUrl: urlToTry,
            attempts,
          };
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : "Unknown error";
        
        if (i < retryCount) {
          onRetry?.(attempts, urlToTry);
          // Add cache busting for retry
          urlToTry = this.addCacheBusting(urlToTry);
          await this.delay(this.RETRY_DELAY);
        }
      }
    }

    // If we have a fallback URL, try it
    if (fallbackUrl) {
      try {
        const success = await this.testImageLoad(fallbackUrl);
        if (success) {
          onFallback?.(originalUrl, fallbackUrl);
          return {
            success: true,
            finalUrl: fallbackUrl,
            attempts: attempts + 1,
          };
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : "Fallback failed";
      }
    }

    // All attempts failed
    onFinalError?.(originalUrl);
    return {
      success: false,
      error: lastError,
      attempts,
    };
  }

  /**
   * Tests if an image can be loaded successfully
   */
  private static testImageLoad(url: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = "anonymous";
      
      img.onload = () => resolve(true);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      
      img.src = url;
    });
  }

  /**
   * Adds cache busting parameter to URL
   */
  private static addCacheBusting(url: string): string {
    const timestamp = Date.now();
    const separator = url.includes("?") ? "&" : "?";
    return `${url}${separator}t=${timestamp}`;
  }

  /**
   * Delays execution for specified milliseconds
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Creates a fallback avatar data URL with initials
   */
  static createFallbackAvatar(
    name: string = "?",
    bgColor: string = "#fcaa96", // TheTribeLab orange
    textColor: string = "#ffffff"
  ): string {
    // For server-side rendering, return empty string
    if (typeof document === "undefined") return "";

    const canvas = document.createElement("canvas");
    canvas.width = 200;
    canvas.height = 200;

    const ctx = canvas.getContext("2d");
    if (!ctx) return "";

    // Draw background
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw text
    const letter = name.charAt(0).toUpperCase();
    ctx.fillStyle = textColor;
    ctx.font = "bold 100px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(letter, canvas.width / 2, canvas.height / 2);

    return canvas.toDataURL("image/png");
  }

  /**
   * Handles image error events with enhanced retry logic
   */
  static handleImageError(
    event: React.SyntheticEvent<HTMLImageElement, Event>,
    options: ImageErrorHandlerOptions & { name?: string } = {}
  ): void {
    const imgElement = event.currentTarget;
    const originalSrc = imgElement.src;
    const { name = "?", ...handlerOptions } = options;

    // Prevent infinite retry loops
    if (imgElement.dataset.retryAttempt) {
      const attempts = parseInt(imgElement.dataset.retryAttempt, 10);
      if (attempts >= (handlerOptions.retryCount || this.DEFAULT_RETRY_COUNT)) {
        this.applyFallback(imgElement, name);
        return;
      }
    }

    // Special handling for Google profile images - skip retry
    if (originalSrc.includes("googleusercontent.com")) {
      this.applyFallback(imgElement, name);
      return;
    }

    // Try with cache busting
    if (!originalSrc.includes("t=")) {
      const newSrc = this.addCacheBusting(originalSrc);
      imgElement.dataset.retryAttempt = "1";
      imgElement.src = newSrc;
    } else {
      this.applyFallback(imgElement, name);
    }
  }

  /**
   * Applies fallback UI when image loading fails
   */
  private static applyFallback(imgElement: HTMLImageElement, name: string): void {
    imgElement.style.display = "none";

    if (imgElement.parentElement) {
      const letter = name.charAt(0).toUpperCase();
      const fallbackHtml = `
        <div class="w-full h-full flex items-center justify-center text-white font-bold text-lg" 
             style="background-color: #fcaa96;">
          ${letter}
        </div>
      `;
      imgElement.parentElement.innerHTML = fallbackHtml;
    }
  }
}

/**
 * React hook for enhanced image loading with error handling
 */
export function useImageWithFallback(
  src: string,
  options: ImageErrorHandlerOptions = {}
) {
  const [imageState, setImageState] = React.useState<{
    src: string;
    loading: boolean;
    error: boolean;
    attempts: number;
  }>({
    src,
    loading: true,
    error: false,
    attempts: 0,
  });

  React.useEffect(() => {
    if (!src) {
      setImageState(prev => ({ ...prev, loading: false, error: true }));
      return;
    }

    setImageState(prev => ({ ...prev, loading: true, error: false }));

    ImageErrorHandler.loadImageWithRetry(src, {
      ...options,
      onRetry: (attempt, url) => {
        setImageState(prev => ({ ...prev, attempts: attempt }));
        options.onRetry?.(attempt, url);
      },
      onFallback: (originalUrl, fallbackUrl) => {
        setImageState(prev => ({ 
          ...prev, 
          src: fallbackUrl || originalUrl,
          loading: false 
        }));
        options.onFallback?.(originalUrl, fallbackUrl);
      },
      onFinalError: (originalUrl) => {
        setImageState(prev => ({ 
          ...prev, 
          loading: false, 
          error: true 
        }));
        options.onFinalError?.(originalUrl);
      },
    }).then(result => {
      if (result.success && result.finalUrl) {
        setImageState(prev => ({
          ...prev,
          src: result.finalUrl!,
          loading: false,
          attempts: result.attempts,
        }));
      }
    });
  }, [src]);

  return imageState;
}

// For environments where React is not available
declare global {
  var React: any;
}

if (typeof React === 'undefined') {
  // Provide a mock implementation for non-React environments
  global.React = {
    useState: () => [null, () => {}],
    useEffect: () => {},
  };
}
